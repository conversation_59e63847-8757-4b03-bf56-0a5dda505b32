package api_test

import (
	"encoding/json"

	"github.com/google/uuid"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2"
)

var _ = Describe("AcceptCardsTerms", func() {
	var (
		client *hcl.Client
		//errSetup error
	)

	const (
		userIDMissing = `{
							"code": "INVALID_PARAMETERS",
									"message": "request has invalid parameter(s) or header(s).",
									"errors": [
              							{
                							"errorCode": "FIELD_MISSING",
                							"message": "'X-Grab-Id-Userid' is missing.",
                							"path": "X-Grab-Id-Userid"
              							}
									 ]
						   }`
	)
	var (
		apiURL = "/v1/card/terms"
	)
	BeforeEach(func() {
		client, _ = hcl.NewClient(server.URL())
	})

	Context("Invalid request Header parameters", func() {
		When("X-grab-Id-User-Id is missing in the header", func() {
			userIDModifier := []hcl.RequestModifier{
				hcl.Header(context.HeaderXUserID, ""),
			}
			userIDModifier = injectIdentityHeader(userIDModifier, servicename.SentryT6)
			It("Returns 403 forbidden", func() {
				resp, err := client.Post(apiURL, userIDModifier...)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(403))
				expectedResponse := userIDMissing
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("accept cards terms and conditions", func() {
		testUserID := uuid.NewString()

		When("happy path", func() {
			userIDModifier := []hcl.RequestModifier{
				hcl.Header(context.HeaderXUserID, testUserID),
			}
			userIDModifier = injectIdentityHeader(userIDModifier, servicename.SentryT6)
			It("Returns 204 OK", func() {
				// AcceptCardsTerms handler passes empty productVariant (uses default agreement ID)
				mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, constant.ProductVariant("")).Return(nil).Once()
				resp, err := client.Post(apiURL, userIDModifier...)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(204))
			})
		})
		When("sad path", func() {
			userIDModifier := []hcl.RequestModifier{
				hcl.Header(context.HeaderXUserID, testUserID),
			}
			userIDModifier = injectIdentityHeader(userIDModifier, servicename.SentryT6)
			It("Returns 500 Internal Server Error", func() {
				// AcceptCardsTerms handler passes empty productVariant (uses default agreement ID)
				mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, constant.ProductVariant("")).Return(api.DefaultInternalServerError).Once()
				resp, err := client.Post(apiURL, userIDModifier...)
				errorResp := &servus.ServiceError{}
				_ = json.Unmarshal(resp.Body.Bytes, &errorResp)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(errorResp.Code).Should(Equal(api.DefaultInternalServerError.Code))
			})
		})
	})
})
