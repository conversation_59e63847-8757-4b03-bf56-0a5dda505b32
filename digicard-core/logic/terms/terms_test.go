package terms

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	mockCustomerMaster "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

func TestClientImpl_PersistCardsTermsAcceptance(t *testing.T) {
	mockCustomerMasterClient := mockCustomerMaster.CustomerMaster{}
	goodCtx := commonCtx.WithUserID(context.Background(), "testID")
	tests := []struct {
		name     string
		ctx      context.Context
		mockfunc func()
		wantErr  error
	}{
		{
			name: "Happy path",
			ctx:  goodCtx,
			mockfunc: func() {
				mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, nil).Once()
			},
			wantErr: nil,
		},
		{
			name: "cm error",
			ctx:  goodCtx,
			mockfunc: func() {
				mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, errors.New("some error")).Once()
			},
			wantErr: api.DefaultInternalServerError,
		},
	}
	c := &ClientImpl{
		StatsD:               statsd.NewNoop(),
		CustomerMasterClient: &mockCustomerMasterClient,
		ProductConfigs:       constant.ProductConfigs,
	}
	for _, tt := range tests {
		tt := tt
		tt.mockfunc()
		t.Run(tt.name, func(t *testing.T) {
			err := c.PersistCardsTermsAcceptance(tt.ctx) // No product variant in context uses default
			if tt.wantErr != nil {
				assert.Equal(t, tt.wantErr, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

func TestClientImpl_PersistCardsTermsAcceptanceWithProductConfig(t *testing.T) {
	mockCustomerMasterClient := mockCustomerMaster.CustomerMaster{}
	goodCtx := commonCtx.WithUserID(context.Background(), "testID")

	t.Run("CCC product variant uses default agreement ID", func(t *testing.T) {
		mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, nil).Once()

		c := &ClientImpl{
			StatsD:               statsd.NewNoop(),
			CustomerMasterClient: &mockCustomerMasterClient,
			ProductConfigs:       constant.ProductConfigs,
		}

		err := c.PersistCardsTermsAcceptance(goodCtx, constant.MYCreditCard)
		assert.NoError(t, err)
		mockCustomerMasterClient.AssertExpectations(t)
	})

	t.Run("debit product variant uses debit agreement ID", func(t *testing.T) {
		mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, nil).Once()

		c := &ClientImpl{
			StatsD:               statsd.NewNoop(),
			CustomerMasterClient: &mockCustomerMasterClient,
			ProductConfigs:       constant.ProductConfigs,
		}

		err := c.PersistCardsTermsAcceptance(goodCtx, constant.MYDebitCard)
		assert.NoError(t, err)
		mockCustomerMasterClient.AssertExpectations(t)
	})

	t.Run("empty product variant uses default agreement ID", func(t *testing.T) {
		mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, nil).Once()

		c := &ClientImpl{
			StatsD:               statsd.NewNoop(),
			CustomerMasterClient: &mockCustomerMasterClient,
			ProductConfigs:       constant.ProductConfigs,
		}

		err := c.PersistCardsTermsAcceptance(goodCtx, "")
		assert.NoError(t, err)
		mockCustomerMasterClient.AssertExpectations(t)
	})

	t.Run("unknown product variant falls back to default", func(t *testing.T) {
		mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, nil).Once()

		c := &ClientImpl{
			StatsD:               statsd.NewNoop(),
			CustomerMasterClient: &mockCustomerMasterClient,
			ProductConfigs:       constant.ProductConfigs,
		}

		err := c.PersistCardsTermsAcceptance(goodCtx, "unknown_variant")
		assert.NoError(t, err)
		mockCustomerMasterClient.AssertExpectations(t)
	})

	t.Run("customer master error with product variant", func(t *testing.T) {
		expectedError := errors.New("customer master error")
		mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, expectedError).Once()

		c := &ClientImpl{
			StatsD:               statsd.NewNoop(),
			CustomerMasterClient: &mockCustomerMasterClient,
			ProductConfigs:       constant.ProductConfigs,
		}

		err := c.PersistCardsTermsAcceptance(goodCtx, constant.MYCreditCard)
		assert.Error(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		mockCustomerMasterClient.AssertExpectations(t)
	})
}
