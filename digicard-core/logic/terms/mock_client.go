// Code generated by mockery v2.53.3. DO NOT EDIT.

package terms

import (
	context "context"

	constant "gitlab.com/gx-regional/dbmy/digicard/common/constant"

	mock "github.com/stretchr/testify/mock"
)

// MockClient is an autogenerated mock type for the Client type
type MockClient struct {
	mock.Mock
}

// PersistCardsTermsAcceptance provides a mock function with given fields: ctx
func (_m *MockClient) PersistCardsTermsAcceptance(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for PersistCardsTermsAcceptance")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetCurrentProductConfig provides a mock function with given fields: productConfig
func (_m *MockClient) SetCurrentProductConfig(productConfig *constant.ProductConfig) {
	_m.Called(productConfig)
}

// NewMockClient creates a new instance of MockClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockClient {
	mock := &MockClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
