package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/service/s3"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/s3utils"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/dto"
	digiCard "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card"
	card_design_utils "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/utils/cache"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/digicard_activity_tx"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
	customerMasterDBMYAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"
)

// ExecuteEventAsync ...
func ExecuteEventAsync(requestID string, event workflowengine.Event, logTag string, workflowID string, params interface{}) func(ctx context.Context) error {
	return func(ctx context.Context) error {
		slog.FromContext(ctx).Info(logTag, fmt.Sprintf("start executing event %d", event))

		_, err := workflowengine.Execute(ctx, workflowengine.Execution{
			WorkflowID:     workflowID,
			RequestID:      requestID,
			ExecutionEvent: event,
		}, params)
		if err != nil {
			slog.FromContext(ctx).Warn(logTag, "error when running workflow", slog.Error(err))
			return err
		}
		return nil
	}
}

// FetchAllCards ...
func FetchAllCards(ctx context.Context, userID string) ([]*storage.Card, error) {
	query := []data.Condition{
		data.EqualTo("UserID", userID),
	}
	querySearch, err := storage.CardDao.Find(ctx, query...)
	if err != nil {
		slog.FromContext(ctx).Debug("fetchAllCards", "error in finding cards for user", slog.Error(err))
		if err == data.ErrNoData {
			return []*storage.Card{{}}, nil
		}
		return nil, api.DefaultInternalServerError
	}
	return querySearch, nil
}

// FetchCardsByProductVariant fetches all cards for a user filtered by product variant
func FetchCardsByProductVariant(ctx context.Context, userID string, productVariant string) ([]*storage.Card, error) {
	// Fetch all cards for the user first
	allCards, err := FetchAllCards(ctx, userID)
	if err != nil {
		slog.FromContext(ctx).Debug("fetchCardsByProductVariant", "error in finding cards for user",
			slog.Error(err), slog.CustomTag("productVariant", productVariant))
		return nil, err
	}

	var filteredCards []*storage.Card
	for _, card := range allCards {
		if card.ProductVariant == productVariant ||
			(card.ProductVariant == "" && productVariant != "") {
			filteredCards = append(filteredCards, card)
		}
	}

	return filteredCards, nil
}

// loadCardDesignsInRedisCache ...
func loadCardDesignsInRedisCache(ctx context.Context) ([]*storage.CardDesign, error) {
	cardDesigns, err := storage.CardDesignDao.Find(ctx, nil...)
	if err != nil {
		slog.FromContext(ctx).Info("FetchAllCardDesignsFromDB", "fetching card designs from database")
		return nil, err
	}
	_ = cache.Set(ctx, string(constant.CardDesignRedisCacheKey), cardDesigns, 600)
	return cardDesigns, nil
}

// FetchAllCardDesigns ...
func FetchAllCardDesigns(ctx context.Context) ([]*storage.CardDesign, error) {
	var cardDesigns []*storage.CardDesign
	if err := cache.Get(ctx, string(constant.CardDesignRedisCacheKey), &cardDesigns); err != nil {
		slog.FromContext(ctx).Info("FetchAllCardDesignsFromCache", fmt.Sprintf("Issue while fetching designs from cache. %s", err.Error()))
		cardDesigns, err = loadCardDesignsInRedisCache(ctx)
		if err != nil {
			return nil, err
		}
	} else {
		slog.FromContext(ctx).Info("FetchAllCardDesigns", "fetching card designs from cache")
	}
	return cardDesigns, nil
}

// FetchCardDesignByCardDesignID ...
func FetchCardDesignByCardDesignID(ctx context.Context, cardDesignID string) (*storage.CardDesign, error) {
	cardDesigns, err := FetchAllCardDesigns(ctx)

	if err != nil {
		return nil, err
	}

	filteredCardDesign, err := findCardDesignByCardDesignID(cardDesigns, cardDesignID)

	if err != nil {
		if err == data.ErrNoData {
			slog.FromContext(ctx).Warn("FetchCardDesignByCardDesignID", fmt.Sprintf("invalid card design ID %s", cardDesignID))
			errorCode := api.ResourceConflict
			return nil, servus.ServiceError{
				Code:     string(errorCode),
				Message:  "invalid Card Design Id.",
				HTTPCode: errorCode.HTTPStatusCode(),
			}
		}
		slog.FromContext(ctx).Warn("FetchCardDesignByCardDesignID", fmt.Sprintf("Issue while fetching card design by Id. %s", err.Error()))
		return nil, api.DefaultInternalServerError
	}
	return &filteredCardDesign, nil
}

// findCardDesignByCardDesignID ...
func findCardDesignByCardDesignID(cardDesigns []*storage.CardDesign, cardDesignID string) (storage.CardDesign, error) {
	for _, cardDesign := range cardDesigns {
		if cardDesign.CardDesignID == cardDesignID {
			return *cardDesign, nil
		}
	}

	return storage.CardDesign{}, data.ErrNoData
}

// OverwriteCardDesignStockCount ...
func OverwriteCardDesignStockCount(ctx context.Context, cardDesignID string, value uint64) (*storage.CardDesign, error) {
	filteredCardDesign, err := FetchCardDesignByCardDesignID(ctx, cardDesignID)
	if err != nil {
		return nil, err
	}
	// Return same card design object if no change to stock count
	if filteredCardDesign.StockCount == value {
		return filteredCardDesign, nil
	}

	updatedCardDesign := &storage.CardDesign{
		ID:           filteredCardDesign.ID,
		CardDesignID: filteredCardDesign.CardDesignID,
		Color:        filteredCardDesign.Color,
		Description:  filteredCardDesign.Description,
		FrontImage:   filteredCardDesign.FrontImage,
		BackImage:    filteredCardDesign.BackImage,
		StockCount:   value,
		CreatedAt:    filteredCardDesign.CreatedAt,
		UpdatedAt:    filteredCardDesign.UpdatedAt,
		NameColor:    filteredCardDesign.NameColor,
	}

	err = storage.CardDesignDao.UpdateEntity(ctx, filteredCardDesign, updatedCardDesign)
	if err != nil {
		slog.FromContext(ctx).Warn("UpdateCardDesignStockCount", fmt.Sprintf("Issue while updating card design stock count. %s", err.Error()))
		return nil, err
	}

	_, _ = loadCardDesignsInRedisCache(ctx)
	return updatedCardDesign, nil
}

// UpdateCardDesignStockCount ...
func UpdateCardDesignStockCount(ctx context.Context, cardDesignID string) error {
	filteredCardDesign, err := FetchCardDesignByCardDesignID(ctx, cardDesignID)
	if err != nil {
		return err
	}

	if filteredCardDesign.StockCount < card_design_utils.CardDesignBenchmark {
		errorCode := api.ResourceConflict
		return &servus.ServiceError{
			Code:     string(errorCode),
			Message:  "Card design is not available.",
			HTTPCode: errorCode.HTTPStatusCode(),
		}
	}

	updatedCardDesign := &storage.CardDesign{
		ID:           filteredCardDesign.ID,
		CardDesignID: filteredCardDesign.CardDesignID,
		Color:        filteredCardDesign.Color,
		Description:  filteredCardDesign.Description,
		FrontImage:   filteredCardDesign.FrontImage,
		BackImage:    filteredCardDesign.BackImage,
		StockCount:   filteredCardDesign.StockCount,
		CreatedAt:    filteredCardDesign.CreatedAt,
		UpdatedAt:    filteredCardDesign.UpdatedAt,
		NameColor:    filteredCardDesign.NameColor,
	}
	updatedCardDesign.StockCount = updatedCardDesign.StockCount - 1

	err = storage.CardDesignDao.UpdateEntity(ctx, filteredCardDesign, updatedCardDesign)
	if err != nil {
		slog.FromContext(ctx).Warn("UpdateCardDesignStockCount", fmt.Sprintf("Issue while updating card design stock count. %s", err.Error()))
		return nil
	}

	_, _ = loadCardDesignsInRedisCache(ctx)
	return nil
}

// RollBackCardDesignStockCountUpdate ...
func RollBackCardDesignStockCountUpdate(ctx context.Context, cardDesignID string) error {
	filteredCardDesign, err := FetchCardDesignByCardDesignID(ctx, cardDesignID)
	if err != nil {
		return err
	}

	updatedCardDesign := &storage.CardDesign{
		ID:           filteredCardDesign.ID,
		CardDesignID: filteredCardDesign.CardDesignID,
		Color:        filteredCardDesign.Color,
		Description:  filteredCardDesign.Description,
		FrontImage:   filteredCardDesign.FrontImage,
		BackImage:    filteredCardDesign.BackImage,
		StockCount:   filteredCardDesign.StockCount,
		CreatedAt:    filteredCardDesign.CreatedAt,
		UpdatedAt:    filteredCardDesign.UpdatedAt,
		NameColor:    filteredCardDesign.NameColor,
	}
	updatedCardDesign.StockCount = updatedCardDesign.StockCount + 1

	err = storage.CardDesignDao.UpdateEntity(ctx, filteredCardDesign, updatedCardDesign)
	if err != nil {
		slog.FromContext(ctx).Warn("UpdateCardDesignStockCount", fmt.Sprintf("Issue while updating card design stock count. %s", err.Error()))
		return nil
	}

	_, _ = loadCardDesignsInRedisCache(ctx)
	return nil
}

// GetPhysicalCardDeliveryInfo ...
func GetPhysicalCardDeliveryInfo(deliveryConfig config.PhysicalCardDeliveryConfig, card *storage.Card) *api.PhysicalCardDeliveryInfo {
	cardOrderStatus := GetOrderStatus(card)

	deliveryInfo := &api.PhysicalCardDeliveryInfo{}
	if card == nil || cardOrderStatus == "" || cardOrderStatus != string(constant.OrderStatusOrdered) {
		if cardOrderStatus == string(constant.OrderStatusProcessing) {
			deliveryInfo.DeliveryStatus = constant.DeliveryStatusProcessing
		}
		return deliveryInfo
	}

	if card.PhysicalCardOrderedDate.IsZero() {
		return deliveryInfo
	}

	today := time.Now()
	difference := today.Sub(card.PhysicalCardOrderedDate)
	days := int64(difference.Hours() / 24)
	var status string
	switch {
	case days <= deliveryConfig.OrderConfirmed:
		status = constant.DeliveryStatusOrderConfirmed
	case days <= deliveryConfig.Posted:
		status = constant.DeliveryStatusPosted
	case days >= deliveryConfig.NotReceivedYet:
		status = constant.DeliveryStatusNotReceived
	default:
		status = ""
	}
	deliveryInfo.CanActivateCard = false
	deliveryInfo.CanViewHelpCenter = false
	deliveryInfo.CanView3RdMonthNotification = false
	deliveryInfo.CanView9ThMonthNotification = false
	deliveryInfo.DeliveryStatus = status
	if days >= deliveryConfig.CanActivateCard {
		deliveryInfo.CanActivateCard = true
	}
	if days >= deliveryConfig.CanViewHelpCenter {
		deliveryInfo.CanViewHelpCenter = true
	}
	if cardOrderStatus != string(constant.OrderStatusActivated) {
		if today.After(card.PhysicalCardOrderedDate.AddDate(0, 9, 0)) {
			deliveryInfo.CanView9ThMonthNotification = true
			deliveryInfo.CanActivateCard = false
			deliveryInfo.DeliveryStatus = ""
		} else if today.After(card.PhysicalCardOrderedDate.AddDate(0, 3, 0)) {
			deliveryInfo.CanView3RdMonthNotification = true
		}
	}
	estimatedArrivalDays := int(deliveryConfig.EstimatedArrivalDays) + int(deliveryConfig.EstimatedArrivalDaysBuffer)
	deliveryInfo.EstimatedCardArrivalDate = card.PhysicalCardOrderedDate.AddDate(0, 0, estimatedArrivalDays).Format("2006-01-02")
	deliveryInfo.DeliveryTracker = GenerateTrackerUrl(deliveryConfig, card)
	return deliveryInfo
}

// GenerateTrackerUrl ...
func GenerateTrackerUrl(deliveryConfig config.PhysicalCardDeliveryConfig, card *storage.Card) *api.DeliveryTracker {
	deliveryTracker := &api.DeliveryTracker{}
	postScript, _ := dto.ConvertJSONToPostScript(card.PostScript)
	if postScript != nil && postScript.ConsignmentNumber != "" {
		baseUrl, ok := deliveryConfig.CourierList[postScript.CourierName]
		if ok {
			fullUrl := strings.ReplaceAll(baseUrl, "consignmentNo", postScript.ConsignmentNumber)
			deliveryTracker.CourierName = postScript.CourierName
			deliveryTracker.TrackerUrl = fullUrl
			return deliveryTracker
		}
	}
	return nil
}

// GetCardType ...
func GetCardType(card *storage.Card) string {
	cardOrderStatus := GetOrderStatus(card)
	switch cardOrderStatus {
	case string(constant.OrderStatusActivated):
		return "PHYSICAL"
	default:
		return "VIRTUAL"
	}
}

// GetOrderStatus ...
func GetOrderStatus(card *storage.Card) string {
	if card.OrderStatus == string(constant.OrderStatusActivated) && card.SetPinAt.IsZero() {
		return string(constant.OrderStatusOrdered)
	}
	return card.OrderStatus
}

// ConvertToMapOfStringToMapOfInterface ...
func ConvertToMapOfStringToMapOfInterface(mapOfString map[string]string) map[string]interface{} {
	mapOfInterface := make(map[string]interface{})
	for index, element := range mapOfString {
		mapOfInterface[index] = element
	}
	return mapOfInterface
}

// FetchAvailableCardDesigns ...
func FetchAvailableCardDesigns(ctx context.Context) ([]*storage.CardDesign, error) {
	cardDesigns, err := FetchAllCardDesigns(ctx)
	var availableCardDesigns []*storage.CardDesign
	if err != nil {
		return nil, err
	}

	for _, cardDesign := range cardDesigns {
		if cardDesign.StockCount >= card_design_utils.CardDesignBenchmark {
			availableCardDesigns = append(availableCardDesigns, cardDesign)
		}
	}

	return availableCardDesigns, nil
}

// FetchAvailableCardDesignsByProductVariant fetches available card designs filtered by product variant
func FetchAvailableCardDesignsByProductVariant(ctx context.Context, productVariant string) ([]*storage.CardDesign, error) {
	// Get all designs from cache/database
	cardDesigns, err := FetchAllCardDesigns(ctx)
	if err != nil {
		return nil, err
	}

	// Filter by product variant and stock availability
	var availableDesigns []*storage.CardDesign
	for _, cardDesign := range cardDesigns {
		// Skip if not in stock
		if cardDesign.StockCount < card_design_utils.CardDesignBenchmark {
			continue
		}

		// Match exact product variant (no fallback to empty variants needed)
		if cardDesign.ProductVariant == productVariant {
			availableDesigns = append(availableDesigns, cardDesign)
		}
	}

	return availableDesigns, nil
}

// FetchCardByCardAndUserID ...
func FetchCardByCardAndUserID(ctx context.Context, userID string, cardID string) (*storage.Card, error) {
	query := []data.Condition{
		data.EqualTo("UserID", userID),
		data.EqualTo("CardID", cardID),
	}
	querySearch, err := storage.CardDao.Find(ctx, query...)
	if err != nil {
		errorCode := api.BadRequest
		return nil, servus.ServiceError{
			Code:     string(api.CardNotFound),
			Message:  "Card not found",
			HTTPCode: errorCode.HTTPStatusCode(),
		}
	}
	return querySearch[0], nil
}

// FetchCardByCardOrderedAt ...
func FetchCardByCardOrderedAt(ctx context.Context, orderedAt string) ([]*storage.Card, error) {
	startDate := fmt.Sprintf("%s %s", orderedAt, StartDateTime)
	endDate := fmt.Sprintf("%s %s", orderedAt, EndDateTime)
	query := []data.Condition{
		data.GreaterThan("PhysicalCardOrderedDate", startDate),
		data.LessThan("PhysicalCardOrderedDate", endDate),
		data.EqualTo("OrderStatus", constant.OrderStatusOrdered),
		data.EqualTo("Status", constant.CardStatusActive),
	}

	querySearch, err := storage.CardDao.Find(ctx, query...)
	if err != nil {
		return nil, err
	}
	return querySearch, nil
}

// GenerateAlphaNumericCode ...
func GenerateAlphaNumericCode(length int) string {
	var letters = []rune("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")

	s := make([]rune, length)
	for i := range s {
		//nolint
		s[i] = letters[rand.Intn(len(letters))]
	}
	return string(s)
}

// ConstructTxObj constructs a digicard activity stream data object from activity dto
func ConstructTxObj(activityDTO *dto.Activity) *digicard_activity_tx.DigicardActivityTx {
	digicardActivityTx := &digicard_activity_tx.DigicardActivityTx{
		EventType:               activityDTO.EventType,
		CardID:                  activityDTO.CardID,
		UserID:                  activityDTO.UserID,
		CardStatus:              activityDTO.CardStatus,
		OrderStatus:             activityDTO.OrderStatus,
		TailCardNumber:          activityDTO.TailCardNumber,
		ToggleFeature:           activityDTO.ToggleFeature,
		IPAddress:               activityDTO.IPAddress,
		DeviceID:                activityDTO.DeviceID,
		Address:                 activityDTO.Address,
		ActivityTimestamp:       activityDTO.ActivityTimestamp,
		Status:                  activityDTO.Status,
		StatusReason:            activityDTO.StatusReason,
		StatusReasonDescription: activityDTO.StatusReasonDescription,
	}

	return digicardActivityTx
}

// PublishCardActivity publishes card activity event to digicard activity stream
func PublishCardActivity(ctx context.Context, publisher digiCard.Publisher, card *storage.Card, eventType CardEventType, logTag string) error {
	deviceID := commonCtx.GetDeviceID(ctx)
	ipAddress := commonCtx.GetTrueClientIP(ctx)
	if err := publisher.Publish(ctx, ActivityDTOFromCard(card, deviceID, ipAddress, eventType), logTag); err != nil {
		return err
	}
	return nil
}

// ActivityDTOFromCard generates card activity dto from card storage
func ActivityDTOFromCard(card *storage.Card, deviceID, ipAddress string, eventType CardEventType) *dto.Activity {
	return &dto.Activity{
		EventType:         string(eventType),
		CardID:            card.CardID,
		UserID:            card.UserID,
		CardStatus:        card.Status,
		OrderStatus:       card.OrderStatus,
		DeviceID:          deviceID,
		IPAddress:         ipAddress,
		ActivityTimestamp: card.PhysicalCardActivatedAt,
	}
}

func ValidateCardTxnToggleAction(cardStatus constant.CardStatus, orderStatus constant.OrderStatus, toggleType string) bool {
	switch cardStatus {
	case constant.CardStatusActive, constant.CardStatusLocked:
		switch orderStatus {
		case constant.OrderStatusNo, constant.OrderStatusProcessing, constant.OrderStatusOrdered:
			return toggleType == constant.CnpTXN
		case constant.OrderStatusActivated:
			return toggleType == constant.CnpTXN || toggleType == constant.OverseasPOSTXN
		}
	}
	return false
}

func ResolveUserName(ctx context.Context, safeID string, client customerMasterDBMYAPI.CustomerMaster) string {
	slog.FromContext(ctx).Debug("ResolveUserName", "calling customer master - GetCustomer")
	customerObj, err := client.GetCustomer(ctx, &customerMasterDBMYAPI.GetCustomerRequest{
		ID: safeID,
		Target: &customerMasterDBMYAPI.TargetGroup{
			ServiceID: constant.ServiceID,
		},
	})
	if err != nil {
		slog.FromContext(ctx).Warn("ResolveUserName", "CustomerMasterClient.GetCustomer error", slog.Error(err))
		return ""
	}
	customerData := &customerMasterDBMYAPI.Customer{}
	if err = json.Unmarshal(customerObj.Customer.Data, customerData); err != nil {
		return ""
	}

	// customer preferredName can be nil if not set
	preferredName := *customerData.Name
	if customerData.PreferredName != nil {
		preferredName = *customerData.PreferredName
	}

	return preferredName
}

func FetchCourierName(courierList map[string]string) string {
	for key := range courierList {
		return key
	}
	return ""
}

func FetchFromS3(ctx context.Context, logTag string, key string) ([]byte, error) {
	body, err := s3utils.S3Client.GetObject(s3utils.S3Bucket, key)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("error fetching %s file from S3", key), slog.Error(err))
		return nil, err
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("Successfully fetched %s file from S3", key))
	return body, nil
}

func ListFileFromS3(ctx context.Context, logTag string, prefix string, maxKey int) ([]s3.Object, error) {
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("Getting file with prefix %s from S3", prefix))
	fileList, err := s3utils.S3Client.ListObjectsWithPrefix(ctx, s3utils.S3Bucket, prefix, maxKey)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Error listing file with prefix %s from S3", prefix), slog.Error(err))
		return nil, err
	}

	return fileList, nil
}
