package handlers

import (
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/terms"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestDigicardCoreService_AcceptCardsTerms(t *testing.T) {
	testCtx := context.Background()
	testCtx = commonCtx.WithUserID(testCtx, "testID")
	mockCardsTermsClient := &terms.MockClient{}
	service := DigicardCoreService{
		CardsTermsClient: mockCardsTermsClient,
	}
	t.Run("Happy path", func(t *testing.T) {
		mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, constant.ProductVariant("")).Return(nil).Once()
		err := service.AcceptCardsTerms(testCtx)
		assert.Nil(t, err)
	})
	t.Run("no userID found", func(t *testing.T) {
		err := service.AcceptCardsTerms(context.Background())
		assert.Equal(t, err, logic.CustomError(http.StatusForbidden, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "'X-Grab-Id-Userid' is missing.",
				Path:      "X-Grab-Id-Userid",
			},
		}))
	})
	t.Run("sad path", func(t *testing.T) {
		mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, mock.Anything).Return(api.DefaultInternalServerError).Once()
		err := service.AcceptCardsTerms(testCtx)
		assert.Equal(t, err, api.DefaultInternalServerError)
	})
}
